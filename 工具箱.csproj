<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="15.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="..\packages\Costura.Fody.6.0.0\build\Costura.Fody.props" Condition="Exists('..\packages\Costura.Fody.6.0.0\build\Costura.Fody.props')" />
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{0C116ABA-D947-40C3-8EF4-14A9B81EC677}</ProjectGuid>
    <OutputType>WinExe</OutputType>
    <RootNamespace>工具箱</RootNamespace>
    <AssemblyName>工具箱</AssemblyName>
    <TargetFrameworkVersion>v4.5.2</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <AutoGenerateBindingRedirects>true</AutoGenerateBindingRedirects>
    <Deterministic>true</Deterministic>
    <NuGetPackageImportStamp>
    </NuGetPackageImportStamp>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <PlatformTarget>x86</PlatformTarget>
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <PlatformTarget>x86</PlatformTarget>
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup>
    <ApplicationIcon>NVT.ico</ApplicationIcon>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="AntdUI">
      <HintPath>bin\Debug\AntdUI.dll</HintPath>
    </Reference>
    <Reference Include="IrisSkin4, Version=2006.3.22.45, Culture=neutral, PublicKeyToken=127be25a6db25e07, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>bin\Debug\IrisSkin4.dll</HintPath>
    </Reference>
    <Reference Include="Newtonsoft.Json, Version=13.0.0.0, Culture=neutral, PublicKeyToken=30ad4fe6b2a6aeed, processorArchitecture=MSIL">
      <HintPath>packages\Newtonsoft.Json.13.0.3\lib\net45\Newtonsoft.Json.dll</HintPath>
    </Reference>
    <Reference Include="PresentationCore" />
    <Reference Include="System" />
    <Reference Include="System.Core" />
    <Reference Include="System.Management" />
    <Reference Include="System.Windows" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Data" />
    <Reference Include="System.Deployment" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Net.Http" />
    <Reference Include="System.Windows.Forms" />
    <Reference Include="System.Xml" />
    <Reference Include="WindowsBase" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="CleanupProgressForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="CleanupProgressForm.Designer.cs">
      <DependentUpon>CleanupProgressForm.cs</DependentUpon>
    </Compile>
    <Compile Include="FileSearchForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="FileSearchForm.Designer.cs">
      <DependentUpon>FileSearchForm.cs</DependentUpon>
    </Compile>
    <Compile Include="Form1.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Form1.Designer.cs">
      <DependentUpon>Form1.cs</DependentUpon>
    </Compile>
    <Compile Include="Program.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="SystemCleaner.cs" />
    <!-- Core Interfaces -->
    <Compile Include="Core\Interfaces\ITool.cs" />
    <Compile Include="Core\Interfaces\IToolManager.cs" />
    <Compile Include="Core\Interfaces\IConfigurationService.cs" />
    <Compile Include="Core\Interfaces\ILoggingService.cs" />
    <!-- Core Models -->
    <Compile Include="Core\Models\BaseTool.cs" />
    <Compile Include="Core\Models\ToolConfiguration.cs" />
    <Compile Include="Core\Models\ApplicationConfiguration.cs" />
    <!-- Infrastructure Services -->
    <Compile Include="Infrastructure\Services\NullConfigurationService.cs" />
    <Compile Include="Infrastructure\Services\NullLoggingService.cs" />
    <Compile Include="Infrastructure\Services\ToolManager.cs" />
    <!-- Error Handling -->
    <Compile Include="Infrastructure\ErrorHandling\GlobalExceptionHandler.cs" />
    <Compile Include="Infrastructure\ErrorHandling\ErrorModels.cs" />
    <!-- Dependency Injection -->
    <Compile Include="Infrastructure\DependencyInjection\ServiceContainer.cs" />
    <!-- Business Tools - AI -->
    <Compile Include="Business\Tools\AI\ChatGPTTool.cs" />
    <!-- Business Tools - System -->
    <Compile Include="Business\Tools\System\SystemCleanerTool.cs" />
    <!-- Business Tools - Network -->
    <Compile Include="Business\Tools\Network\NetworkTools.cs" />
    <!-- Business Tools - Utility -->
    <Compile Include="Business\Tools\Utility\UtilityTools.cs" />
    <!-- Presentation Services -->
    <Compile Include="Presentation\Services\ApplicationService.cs" />
    <EmbeddedResource Include="CleanupProgressForm.resx">
      <DependentUpon>CleanupProgressForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="FileSearchForm.resx">
      <DependentUpon>FileSearchForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Form1.resx">
      <DependentUpon>Form1.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Properties\Resources.resx">
      <Generator>ResXFileCodeGenerator</Generator>
      <LastGenOutput>Resources.Designer.cs</LastGenOutput>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <Compile Include="Properties\Resources.Designer.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Resources.resx</DependentUpon>
    </Compile>
    <None Include="packages.config" />
    <None Include="Properties\Settings.settings">
      <Generator>SettingsSingleFileGenerator</Generator>
      <LastGenOutput>Settings.Designer.cs</LastGenOutput>
    </None>
    <Compile Include="Properties\Settings.Designer.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Settings.settings</DependentUpon>
      <DesignTimeSharedInput>True</DesignTimeSharedInput>
    </Compile>
  </ItemGroup>
  <ItemGroup>
    <None Include="App.config" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="NVT.ico" />
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <Target Name="EnsureNuGetPackageBuildImports" BeforeTargets="PrepareForBuild">
    <PropertyGroup>
      <ErrorText>这台计算机上缺少此项目引用的 NuGet 程序包。使用“NuGet 程序包还原”可下载这些程序包。有关更多信息，请参见 http://go.microsoft.com/fwlink/?LinkID=322105。缺少的文件是 {0}。</ErrorText>
    </PropertyGroup>
    <Error Condition="!Exists('..\packages\Costura.Fody.6.0.0\build\Costura.Fody.props')" Text="$([System.String]::Format('$(ErrorText)', '..\packages\Costura.Fody.6.0.0\build\Costura.Fody.props'))" />
    <Error Condition="!Exists('..\packages\Costura.Fody.6.0.0\build\Costura.Fody.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\packages\Costura.Fody.6.0.0\build\Costura.Fody.targets'))" />
  </Target>
  <Import Project="..\packages\Costura.Fody.6.0.0\build\Costura.Fody.targets" Condition="Exists('..\packages\Costura.Fody.6.0.0\build\Costura.Fody.targets')" />
</Project>