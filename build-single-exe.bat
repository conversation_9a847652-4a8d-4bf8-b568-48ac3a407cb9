@echo off
echo ========================================
echo 构建单个exe文件
echo ========================================

echo 1. 清理之前的构建文件...
if exist "bin\Release" rmdir /s /q "bin\Release"
if exist "obj\Release" rmdir /s /q "obj\Release"

echo 2. 还原NuGet包...
nuget restore

echo 3. 构建Release版本...
msbuild "工具箱.csproj" /p:Configuration=Release /p:Platform=AnyCPU /p:OutputPath=bin\Release\ /verbosity:minimal

if %ERRORLEVEL% NEQ 0 (
    echo 构建失败！
    pause
    exit /b 1
)

echo 4. 检查构建结果...
if exist "bin\Release\工具箱.exe" (
    echo ✓ 构建成功！
    echo.
    echo 输出文件位置: bin\Release\工具箱.exe
    echo.
    dir "bin\Release\*.exe" /b
    echo.
    echo 文件大小:
    for %%f in ("bin\Release\工具箱.exe") do echo %%~nxf: %%~zf bytes
) else (
    echo ✗ 构建失败，未找到exe文件
)

echo.
echo 构建完成！
pause
