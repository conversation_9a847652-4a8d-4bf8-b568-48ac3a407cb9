# 单exe文件构建指南

## 问题描述
您的工具箱项目需要生成单个exe文件，避免依赖外部DLL文件。

## 解决方案

### 1. 已完成的配置
✅ 已创建 `FodyWeavers.xml` 配置文件
✅ 已优化项目文件 `工具箱.csproj`
✅ 已安装 Costura.Fody 包

### 2. 构建步骤

#### 方法一：使用Visual Studio
1. 打开Visual Studio
2. 加载项目 `工具箱.csproj`
3. 选择 **Release** 配置
4. 右键项目 → **重新生成**
5. 生成的单exe文件位于：`bin\Release\工具箱.exe`

#### 方法二：使用开发者命令提示符
1. 打开 **Visual Studio Developer Command Prompt**
2. 导航到项目目录
3. 执行命令：
```cmd
msbuild "工具箱.csproj" /p:Configuration=Release /p:Platform=AnyCPU
```

#### 方法三：使用批处理脚本
运行项目根目录下的 `build-single-exe.bat`

### 3. 验证单exe文件
构建完成后，检查以下内容：

1. **文件大小**：单exe文件应该比原来大很多（包含了所有依赖项）
2. **独立运行**：将exe文件复制到其他目录，应该能独立运行
3. **无外部依赖**：运行时不需要其他DLL文件

### 4. 可能的问题和解决方案

#### 问题1：本地DLL未嵌入
如果 `AntdUI.dll` 或 `IrisSkin4.dll` 未正确嵌入：

1. 确保这些DLL在项目的 `bin\Debug` 或 `bin\Release` 目录中
2. 检查 `FodyWeavers.xml` 中的配置
3. 可能需要将这些DLL添加为嵌入资源

#### 问题2：运行时错误
如果单exe文件运行时出错：

1. 检查是否有配置文件依赖（App.config）
2. 确保所有必要的.NET Framework组件已安装
3. 检查是否有本机依赖项未处理

### 5. 高级配置

如果需要更精细的控制，可以修改 `FodyWeavers.xml`：

```xml
<Costura>
  <!-- 只嵌入特定程序集 -->
  <IncludeAssemblies>
    Newtonsoft.Json
    AntdUI
  </IncludeAssemblies>
  
  <!-- 排除某些程序集 -->
  <ExcludeAssemblies>
    System.*
  </ExcludeAssemblies>
  
  <!-- 预加载程序集 -->
  <Unmanaged32Assemblies>
    IrisSkin4
  </Unmanaged32Assemblies>
</Costura>
```

### 6. 测试清单
- [ ] 构建成功无错误
- [ ] 生成的exe文件大小合理（包含依赖项）
- [ ] 在干净的环境中能独立运行
- [ ] 所有功能正常工作
- [ ] 无外部DLL依赖

## 下一步
请尝试使用Visual Studio重新构建项目，然后测试生成的单exe文件是否能正常工作。
