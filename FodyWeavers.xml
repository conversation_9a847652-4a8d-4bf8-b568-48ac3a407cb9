<?xml version="1.0" encoding="utf-8"?>
<Weavers xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="FodyWeavers.xsd">
  <Costura>
    <!-- 包含所有引用的程序集 -->
    <IncludeAssemblies>
      Newtonsoft.Json
      AntdUI
      IrisSkin4
    </IncludeAssemblies>
    
    <!-- 排除不需要嵌入的程序集 -->
    <ExcludeAssemblies>
      
    </ExcludeAssemblies>
    
    <!-- 创建临时程序集 -->
    <CreateTemporaryAssemblies>true</CreateTemporaryAssemblies>
    
    <!-- 不创建单独的资源文件 -->
    <DisableCompression>false</DisableCompression>
    
    <!-- 不创建单独的配置文件 -->
    <DisableCleanup>false</DisableCleanup>
    
    <!-- 加载本机库 -->
    <LoadAtModuleInit>true</LoadAtModuleInit>
  </Costura>
</Weavers>
